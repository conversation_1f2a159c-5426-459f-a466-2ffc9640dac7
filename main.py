from blitzy_utils.logger import logger
from flask import Flask
from flask_utils.middleware.request_context import RequestContextMiddleware

from src.api.routes.azure import azure_bp
from src.api.routes.azure_users import azure_users_bp
from src.api.routes.github_users import github_users_bp
from src.api.routes.github_webhook import github_bp
from src.api.routes.project_info import project_bp
from src.api.routes.users import users_bp

app = Flask(__name__)

app.register_blueprint(github_bp)
app.register_blueprint(project_bp)
app.register_blueprint(azure_bp)
app.register_blueprint(azure_users_bp)
app.register_blueprint(github_users_bp)
app.register_blueprint(users_bp)

middleware = RequestContextMiddleware(app, logger=logger)


if __name__ == "__main__":
    logger.info("Starting the server...")
    app.run(host="localhost", port=8082, debug=True)
