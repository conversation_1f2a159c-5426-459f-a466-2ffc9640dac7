"""
Test cases for project GitHub repos endpoint with Azure DevOps integration.
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from src.api.routes.project_github_repos import (
    get_azure_repo_name,
    fetch_github_repos_by_project_id
)
from common_models.models import GitHubProjectRepo, VersionControlSystem
from src.api.models import ProjectGithubRepoOutputList


class TestAzureRepoNameIntegration:
    """Test cases for Azure DevOps repository name integration."""

    def test_get_azure_repo_name_success(self):
        """Test successful retrieval of Azure repo name."""
        # Create a mock GitHubProjectRepo with Azure integration
        github_repo = Mock(spec=GitHubProjectRepo)
        github_repo.azure_project_id = "test-project-id"
        github_repo.azure_org_id = "test-org-id"
        github_repo.repo_id = "test-repo-id"
        
        user_id = "test-user-id"
        
        # Mock the Azure service dependencies
        with patch('src.api.routes.project_github_repos.fetch_azure_secret_for_user') as mock_fetch_token, \
             patch('src.api.routes.project_github_repos._get_azure_devops_repo') as mock_get_repo:
            
            # Setup mock token response
            mock_token = Mock()
            mock_token.accessToken = "test-access-token"
            mock_fetch_token.return_value = mock_token
            
            # Setup mock Azure repo response
            mock_get_repo.return_value = {
                'id': 'test-repo-id',
                'name': 'azure-repo-name',
                'url': 'https://dev.azure.com/org/project/_git/repo'
            }
            
            # Call the function
            result = get_azure_repo_name(github_repo, user_id)
            
            # Verify the result
            assert result == 'azure-repo-name'
            
            # Verify the calls
            mock_fetch_token.assert_called_once_with(user_id, VersionControlSystem.AZURE_DEVOPS)
            mock_get_repo.assert_called_once_with(
                organization="test-org-id",
                project_id="test-project-id",
                repo_id="test-repo-id",
                access_token="test-access-token"
            )

    def test_get_azure_repo_name_no_azure_integration(self):
        """Test when GitHub repo has no Azure DevOps integration."""
        # Create a mock GitHubProjectRepo without Azure integration
        github_repo = Mock(spec=GitHubProjectRepo)
        github_repo.azure_project_id = None
        github_repo.azure_org_id = None
        github_repo.repo_id = "test-repo-id"
        
        user_id = "test-user-id"
        
        # Call the function
        result = get_azure_repo_name(github_repo, user_id)
        
        # Verify the result
        assert result is None

    def test_get_azure_repo_name_no_access_token(self):
        """Test when user has no Azure access token."""
        # Create a mock GitHubProjectRepo with Azure integration
        github_repo = Mock(spec=GitHubProjectRepo)
        github_repo.azure_project_id = "test-project-id"
        github_repo.azure_org_id = "test-org-id"
        github_repo.repo_id = "test-repo-id"
        
        user_id = "test-user-id"
        
        # Mock the Azure service dependencies
        with patch('src.api.routes.project_github_repos.fetch_azure_secret_for_user') as mock_fetch_token:
            
            # Setup mock token response (no token)
            mock_fetch_token.return_value = None
            
            # Call the function
            result = get_azure_repo_name(github_repo, user_id)
            
            # Verify the result
            assert result is None

    def test_get_azure_repo_name_azure_repo_not_found(self):
        """Test when Azure repo is not found."""
        # Create a mock GitHubProjectRepo with Azure integration
        github_repo = Mock(spec=GitHubProjectRepo)
        github_repo.azure_project_id = "test-project-id"
        github_repo.azure_org_id = "test-org-id"
        github_repo.repo_id = "test-repo-id"
        
        user_id = "test-user-id"
        
        # Mock the Azure service dependencies
        with patch('src.api.routes.project_github_repos.fetch_azure_secret_for_user') as mock_fetch_token, \
             patch('src.api.routes.project_github_repos._get_azure_devops_repo') as mock_get_repo:
            
            # Setup mock token response
            mock_token = Mock()
            mock_token.accessToken = "test-access-token"
            mock_fetch_token.return_value = mock_token
            
            # Setup mock Azure repo response (repo not found)
            mock_get_repo.return_value = None
            
            # Call the function
            result = get_azure_repo_name(github_repo, user_id)
            
            # Verify the result
            assert result is None

    def test_get_azure_repo_name_exception_handling(self):
        """Test exception handling in get_azure_repo_name."""
        # Create a mock GitHubProjectRepo with Azure integration
        github_repo = Mock(spec=GitHubProjectRepo)
        github_repo.azure_project_id = "test-project-id"
        github_repo.azure_org_id = "test-org-id"
        github_repo.repo_id = "test-repo-id"
        
        user_id = "test-user-id"
        
        # Mock the Azure service dependencies
        with patch('src.api.routes.project_github_repos.fetch_azure_secret_for_user') as mock_fetch_token:
            
            # Setup mock to raise an exception
            mock_fetch_token.side_effect = Exception("Test exception")
            
            # Call the function
            result = get_azure_repo_name(github_repo, user_id)
            
            # Verify the result
            assert result is None

    def test_fetch_github_repos_by_project_id_with_azure_names(self):
        """Test fetch_github_repos_by_project_id includes Azure repo names."""
        project_id = "test-project-id"
        user_id = "test-user-id"
        
        # Create mock GitHub repos
        github_repo1 = Mock(spec=GitHubProjectRepo)
        github_repo1.azure_project_id = "azure-project-1"
        github_repo1.azure_org_id = "azure-org-1"
        github_repo1.repo_id = "repo-1"
        
        github_repo2 = Mock(spec=GitHubProjectRepo)
        github_repo2.azure_project_id = None  # No Azure integration
        github_repo2.azure_org_id = None
        github_repo2.repo_id = "repo-2"
        
        github_repos = [github_repo1, github_repo2]
        
        with patch('src.api.routes.project_github_repos.get_github_project_repo_by_project_id') as mock_get_repos, \
             patch('src.api.routes.project_github_repos.map_to_model') as mock_map_to_model, \
             patch('src.api.routes.project_github_repos.get_azure_repo_name') as mock_get_azure_name:
            
            # Setup mocks
            mock_get_repos.return_value = github_repos
            
            # Mock pydantic models
            mock_model1 = Mock()
            mock_model2 = Mock()
            mock_map_to_model.side_effect = [mock_model1, mock_model2]
            
            # Mock Azure repo name function
            def azure_name_side_effect(repo, uid):
                if repo.repo_id == "repo-1":
                    return "azure-repo-name-1"
                return None
            
            mock_get_azure_name.side_effect = azure_name_side_effect
            
            # Call the function
            result = fetch_github_repos_by_project_id(project_id, user_id)
            
            # Verify the result
            assert isinstance(result, ProjectGithubRepoOutputList)
            assert len(result.results) == 2
            
            # Verify Azure repo name was set for the first repo
            assert mock_model1.azureRepoName == "azure-repo-name-1"
            
            # Verify Azure repo name was not set for the second repo (no Azure integration)
            assert not hasattr(mock_model2, 'azureRepoName') or mock_model2.azureRepoName is None


if __name__ == "__main__":
    pytest.main([__file__])
