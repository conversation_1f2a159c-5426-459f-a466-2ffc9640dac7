from typing import Any, Dict

from blitzy_utils.common import generate_technical_spec_document_path
from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (Project, ProjectRun, ProjectRunStatus,
                                  ReportStatus, Status, TechnicalSpec)

from src.consts import GCS_BUCKET_NAME
from src.event.code_graph_event import attempt_branch_lock_release
from src.service.project_run_service import update_project_run_by_tech_spec_id
from src.service.tech_spec_document_context_service import \
    get_tech_spec_document_context
from src.service.tech_spec_service import (get_tech_spec_by_id,
                                           update_tech_spec_by_id)
from src.utils.gcs_utils import copy_gcs_file


def process_code_download_event(project_info: Project, job_id: str, status: Status, metadata: Dict[str, Any],
                                payload: Dict[str, Any]):
    if status != Status.DONE:
        logger.info("Status is not done. Skipping tech spec context extraction.")
        return

    branch_id = payload["branch_id"]
    head_commit_hash = payload["head_commit_hash"]
    tech_spec_id = payload["tech_spec_id"]

    logger.info(f"Extracting Tech spec context for project {project_info.id}")
    graph_needs_update = payload["graph_needs_update"]

    if graph_needs_update:
        logger.info(f"Graph needs update for project {project_info.id}. This means new tech spec will be generated.")
        logger.info("Skipping. Tech spec copy.")
        return

    tech_spec_context = get_tech_spec_document_context(branch_id, head_commit_hash)
    if not tech_spec_context:
        logger.info(f"Tech spec context not found for project {project_info.id}. Skipping.")
        return
    logger.info(f"Found Tech spec context for project {project_info.id}")

    source_tech_spec_payload = tech_spec_context.context_metadata
    source_path = generate_technical_spec_document_path(source_tech_spec_payload, GCS_BUCKET_NAME)

    tech_spec = get_tech_spec_by_id(tech_spec_id)
    if not tech_spec:
        logger.warning(f"Tech spec {tech_spec_id} does not exist. Event won't be processed.")
        return

    destination_tech_spec_payload = tech_spec.job_metadata
    destination_path = generate_technical_spec_document_path(destination_tech_spec_payload, GCS_BUCKET_NAME)

    try:
        copy_gcs_file(source_path, destination_path)
        logger.info(f"Successfully copied tech spec from {source_path} to {destination_path}")
    except Exception as e:
        logger.error(f"Error during tech spec copy for project {project_info.id}: {str(e)}")
        return

    source_pdf_path = source_path.replace(".md", ".pdf")
    destination_pdf_path = destination_path.replace(".md", ".pdf")

    copy_gcs_file(source_pdf_path, destination_pdf_path)
    logger.info(f"Successfully copied tech spec PDF from {source_path} to {destination_path}")

    with get_db_session() as session:
        tech_spec_payload = {
            TechnicalSpec.status: status,
            TechnicalSpec.pdf_report_status: ReportStatus.READY,
        }

        update_tech_spec_by_id(tech_spec_id, tech_spec_payload, session=session)

        project_run_status = ProjectRunStatus[payload["status"]]
        job_payload = {
            ProjectRun.status: project_run_status,
            ProjectRun.job_metadata: metadata,
        }

        update_project_run_by_tech_spec_id(tech_spec.id, job_payload, session=session)
        attempt_branch_lock_release(branch_id, session)
        session.commit()
        logger.info(f"Tech spec and job metadata updated for the project {project_info.id}")
