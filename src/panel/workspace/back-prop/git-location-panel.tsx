import {useCallback, useEffect, useState} from 'react';
import {SelectItem} from '@/components/custom/select-with-filter';
import {ProjectPanelContent} from '../project-panel-content';
import iconGithub from '../icons/github.svg';
import {useNavigate, useSearchParams} from 'react-router-dom';
import {useProjectContext} from '@/context/project-context';
import {
    getGithubBranches,
    getGithubAccounts,
    getGithubRepositories,
    onboardGitLocations,
    onboardGitLocationsForNewProduct,
} from '@/lib/backend';
import InfoCircle from '../icons/info-circle';
import CodeCurly from '../icons/code-curly';
import Revision from '../icons/revision';
import Wrench from '../icons/wrench';
import {
    GitRepo,
    GitLocationType,
    GitLocation,
    ProjectDocumentType,
} from '@/lib/entity-types';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {PlatformError} from '@/lib/platform-error';
import {useToast} from '@/hooks/use-toast';
import {isValidRepoName, logGAEvent} from '@/lib/utils';
import {ComboSelect} from '@/components/custom/combo-select';
import {Input} from '@/components/ui/input';
import {useGithubInstallation} from '@/panel/workspace/hooks/github-status-hook';
import {UpgradeModal} from '@/modal/upgrade-modal';
import {useSubscriptionContext} from '@/context/subscription-context';
import {useAuthContext} from '@/context/auth-context';
import {useDocumentSubmission} from '@/context/document-submission-context';

type PropsType = {
    type: 'new-product' | 'onboard-code' | 'refactor-code' | 'custom';
};

function validateNewRepoName(repoName: string) {
    let error = '';
    let isValid = true;

    if (!repoName) {
        error = 'Repository name is required';
        isValid = false;
    } else if (repoName.length > 100) {
        error = 'Repository name must be maximum 100 characters';
        isValid = false;
    } else if (!isValidRepoName(repoName)) {
        error =
            'Repository name can only contain ASCII letters, digits, and the characters ., -, and _';
        isValid = false;
    } else {
        error = '';
        isValid = true;
    }

    return {isValid, error};
}

export function GitLocationPanel({type}: PropsType) {
    const [searchParams] = useSearchParams();

    // Get values from URL parameters for fallback
    const urlDocType = searchParams.get('docType') as ProjectDocumentType;

    // Use context data first, then URL params as fallback
    // This allows the git location panel to receive data from the ActionBuildCodebase component
    // when navigating from tech-spec page for NEW_PRODUCT, REFACTOR_CODE, and CUSTOM flows
    const {
        onApprove,
        isDocumentSubmitting,
        setIsDocumentSubmitting,
        userProfile: contextUserProfile,
    } = useDocumentSubmission();

    const [orgList, setOrgList] = useState<SelectItem[]>([]);
    const [orgListLoading, setOrgListLoading] = useState(false);
    const [orgName, setOrgName] = useState('');
    const [repoList, setRepoList] = useState<GitRepo[]>([]);
    const [repoListLoading, setRepoListLoading] = useState(false);
    const [repoId, setRepoId] = useState<string>('');
    const [newRepoName, setNewRepoName] = useState('');
    const [newRepoNameError, setNewRepoNameError] = useState('');
    const [branchList, setBranchList] = useState<SelectItem[]>([]);
    const [branchListLoading, setBranchListLoading] = useState(false);
    const [branchName, setBranchName] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [showUpgradeModal, setShowUpgradeModal] = useState(false);
    const {toast} = useToast();

    const navigateTo = useNavigate();
    const {project, projectState, refreshProjectState, refreshProject} =
        useProjectContext();

    // Add hooks for document submission logic
    const {status, checkGithubInstallationStatus} = useGithubInstallation();
    const {plan, refreshSubscription} = useSubscriptionContext();
    const {userProfile} = useAuthContext();

    useEffect(() => {
        (async () => {
            try {
                setOrgListLoading(true);
                const gitAccounts = await getGithubAccounts();
                const orgList = (gitAccounts ?? []).map(acct => ({
                    label: acct.name,
                    value: acct.name,
                    disabled: false,
                }));
                setOrgList(orgList);
            } catch (error) {
                console.error(error);
            } finally {
                setOrgListLoading(false);
            }
        })();
    }, []);

    useEffect(() => {
        const {error} = validateNewRepoName(newRepoName);
        setNewRepoNameError(error);
    }, [newRepoName]);

    const handleContinue = useCallback(async () => {
        if (projectState?.codeGeneration?.status === 'IN_PROGRESS') {
            toast({
                variant: 'default',
                duration: 10000,
                description: 'Code generation is in progress',
            });
            return;
        }
        try {
            // Validate repository name if creating a new repository
            if (repoId === 'Create new repository') {
                const newRepoNameTrimmed = newRepoName.trim();
                const {isValid, error} =
                    validateNewRepoName(newRepoNameTrimmed);
                setNewRepoNameError(error);
                if (!isValid) return;
            }

            if (!project?.id) return;

            setIsSubmitting(true);

            // Set document submitting state if provided from context
            if (setIsDocumentSubmitting) {
                setIsDocumentSubmitting(true);
            }

            const repo = repoList.find(repo => `${repo.id}` === repoId);
            if (repoId !== 'Create new repository' && !repo) return;
            const gitLocations: GitLocation[] = [];

            const currentLocation = {
                orgName,
                repoId: repoId === 'Create new repository' ? '' : repoId,
                repoName:
                    repoId === 'Create new repository'
                        ? newRepoName
                        : repo?.name || '',
                branchName,
                createRepo: repoId === 'Create new repository',
                type: (type === 'onboard-code'
                    ? 'SOURCE'
                    : 'TARGET') as GitLocationType,
            };

            logGAEvent('git_location_continue_clicked', {
                event_category: 'Project',
                event_label: type,
                project_id: project.id,
                organization: orgName,
                repository:
                    repoId === 'Create new repository'
                        ? newRepoName
                        : repo?.name,
                branch: branchName || 'new_branch',
            });
            gitLocations.push(currentLocation);
            // Add source location for refactor-code and custom projects
            if (
                (type === 'refactor-code' || type === 'custom') &&
                project.gitSource
            ) {
                gitLocations.push({
                    ...project.gitSource,
                });
            }

            // Onboard git locations based on project type
            if (type === 'new-product') {
                await onboardGitLocationsForNewProduct(
                    project.id,
                    currentLocation,
                );
            } else {
                await onboardGitLocations(project.id, gitLocations);
            }

            const proj = await refreshProject?.();
            await refreshProjectState?.({project: proj});

            // Document submission flow for NEW_PRODUCT and REFACTOR_CODE
            // This handles the code generation process after git location setup
            if (
                (type === 'new-product' ||
                    type === 'refactor-code' ||
                    type === 'custom') &&
                onApprove &&
                setIsDocumentSubmitting
            ) {
                await refreshSubscription?.();

                // Check if user has FREE plan and show upgrade modal if needed
                if (
                    plan === 'FREE' ||
                    contextUserProfile?.subscription?.planName === 'FREE'
                ) {
                    setShowUpgradeModal(true);
                    logGAEvent('upgrade_modal_triggered', {
                        event_category: 'Subscription',
                        event_label: 'build_code_attempt',
                        project_id: project.id,
                        current_plan: plan,
                        action_attempted: 'build_code',
                    });
                    setIsDocumentSubmitting(false);
                    setIsSubmitting(false);
                    return;
                }

                // Check GitHub installation status
                await checkGithubInstallationStatus();
                if (urlDocType === 'tech_spec' && status !== 'ACTIVE') {
                    logGAEvent('github_installation_needed', {
                        event_category: 'Project',
                        event_label: type,
                        project_id: project.id,
                        github_status: status,
                    });
                    navigateTo({
                        pathname: `/workspace/project/${project.id}/tech-spec`,
                        search: '?popup=github-enable',
                    });
                    setIsDocumentSubmitting(false);
                    setIsSubmitting(false);
                    return;
                }

                try {
                    // Start code generation process
                    logGAEvent('code_generation_started', {
                        event_category: 'Project',
                        event_label: type,
                        project_id: project.id,
                        action: 'build_code',
                        subscription_plan: userProfile?.subscription?.planName,
                    });
                    await onApprove();

                    // Navigate back to tech-spec page after successful submission
                    navigateTo(`/workspace/project/${project.id}/tech-spec`, {
                        replace: true,
                    });
                } catch (error) {
                    console.error('Error in onApprove:', error);
                }

                setIsDocumentSubmitting(false);
            } else if (type === 'onboard-code') {
                // Navigate to prompt page for onboard-code flow
                navigateTo(
                    `/workspace/project/${project.id}/prompt?action=ONBOARD_CODE`,
                    {replace: true},
                );
            }
        } catch (error: unknown) {
            console.error('Error in handleContinue:', error);
            let message = 'Failed to onboard codebase';
            if (
                error instanceof PlatformError &&
                error.code === 'HttpStatus409'
            ) {
                message = error.message;
            } else if (error instanceof Error) {
                message = error.message;
            }
            toast({
                variant: 'default',
                duration: 10000,
                description: message,
            });
        } finally {
            setIsSubmitting(false);
            if (setIsDocumentSubmitting) {
                setIsDocumentSubmitting(false);
            }
        }
    }, [
        project,
        orgName,
        repoId,
        newRepoName,
        repoList,
        branchName,
        type,
        refreshProjectState,
        refreshProject,
        navigateTo,
        toast,
        onApprove,
        urlDocType,
        status,
        checkGithubInstallationStatus,
        setIsDocumentSubmitting,
        plan,
        contextUserProfile,
        refreshSubscription,
        userProfile,
        projectState,
    ]);

    const handleOrgChange = (org: string) => {
        setOrgName(org);
        setRepoId('');
        setBranchName('');
        setRepoListLoading(true);
        (async () => {
            try {
                const repoList = await getGithubRepositories(org);
                setRepoList(repoList);
            } catch (error) {
                console.error(error);
            } finally {
                setRepoListLoading(false);
                setNewRepoName('');
                setNewRepoNameError('');
            }
        })();
    };

    const handleRepoChange = (repoId: string) => {
        setRepoId(repoId);
        setBranchName('');
        setBranchListLoading(true);
        if (type !== 'onboard-code') return;
        (async () => {
            try {
                const branchList = await getGithubBranches(orgName, repoId);
                setBranchList(
                    branchList.map(branch => ({
                        label: branch.name,
                        value: branch.name,
                        disabled: false,
                        badge: undefined,
                    })),
                );
            } catch (error) {
                console.error(error);
            } finally {
                setBranchListLoading(false);
                setNewRepoName('');
                setNewRepoNameError('');
            }
        })();
    };

    const handleBranchChange = (branch: string) => {
        setBranchName(branch);
    };

    return (
        <ProjectPanelContent>
            <div className="flex flex-col gap-[32px] sm:gap-[48px] pb-[32px]">
                <div>
                    <h2 className="mb-[12px] text-[20px] sm:text-[24px] text-black font-semibold">
                        {type === 'new-product'
                            ? "Where should we put the new product's code?"
                            : type === 'onboard-code'
                              ? 'Import source code'
                              : type === 'refactor-code'
                                ? 'Where should we put the refactored code?'
                                : type === 'custom'
                                  ? 'Where should we put the code?'
                                  : 'Where should we put the code?'}
                    </h2>
                    <p className="text-[16px] text-[#333]">
                        {type === 'new-product'
                            ? 'Select the destination for your codebase'
                            : type === 'onboard-code'
                              ? 'Connect your codebase to get a detailed technical spec, so you can add features or refactor as needed!'
                              : type === 'refactor-code'
                                ? 'Select the destination for your modernized codebase'
                                : type === 'custom'
                                  ? 'Select the destination'
                                  : 'Select the destination'}
                    </p>
                </div>
                <div className="min-h-[300px] flex flex-col sm:flex-row sm:justify-start sm:items-start gap-[32px] sm:gap-[48px]">
                    <div className="sm:px-[48px] py-0 w-full sm:w-[300px] flex flex-row sm:flex-col justify-start sm:justify-center items-center gap-[12px] sm:border-r border-[#D9D9D9] sm:aspect-square">
                        <img
                            src={iconGithub}
                            alt="Github"
                            className="w-[32px] sm:w-[64px] h-[32px] sm:h-[64px]"
                        />
                    </div>
                    <div className="flex-grow flex flex-col gap-[24px]">
                        <div>
                            <label>
                                <span className="inline-block mb-[12px] text-[16px] text-[#333] font-semibold leading-[22px]">
                                    Organization
                                </span>
                                <ComboSelect
                                    value={orgName}
                                    options={orgList}
                                    onChange={handleOrgChange}
                                    placeholder="Select organization"
                                    itemsLoading={orgListLoading}
                                    loadingMessage="Loading..."
                                    emptyMessage="No results found"
                                />
                            </label>
                        </div>
                        {orgName && (
                            <div>
                                <label>
                                    <span className="inline-block mb-[12px] text-[16px] text-[#333] font-semibold leading-[22px]">
                                        Repository
                                    </span>
                                    <ComboSelect
                                        value={repoId}
                                        options={[
                                            ...(type !== 'onboard-code'
                                                ? [
                                                      {
                                                          label: 'Create new repository',
                                                          value: 'Create new repository',
                                                          disabled: false,
                                                      },
                                                  ]
                                                : []),
                                            ...repoList.map(repo => ({
                                                label: repo.name,
                                                value: `${repo.id}`,
                                                disabled: false,
                                            })),
                                        ]}
                                        onChange={handleRepoChange}
                                        placeholder="Select repository"
                                        itemsLoading={repoListLoading}
                                        loadingMessage="Loading..."
                                        emptyMessage="No results found"
                                    />
                                </label>
                            </div>
                        )}
                        {orgName && repoId === 'Create new repository' && (
                            <div>
                                <label>
                                    <span className="inline-block mb-[12px] text-[16px] text-[#333] font-semibold leading-[22px]">
                                        Name your repository
                                    </span>
                                    <Input
                                        value={newRepoName}
                                        onChange={e =>
                                            setNewRepoName(e.target.value)
                                        }
                                        placeholder="Repository name"
                                        className={`w-full h-[48px] p-[12px] text-[16px] gap-[16px] leading-[19px] border border-[#999] rounded-[32px] hover:border-[#D4CBFC] focus:outline-none focus:border-[#D4CBFC] ${newRepoNameError && 'border-[#EC3636]'} placeholder:text-[#999]`}
                                    />
                                    {newRepoNameError && (
                                        <div className="mt-[4px] text-[14px] text-[#EC3636]">
                                            {newRepoNameError}
                                        </div>
                                    )}
                                </label>
                            </div>
                        )}
                        {orgName && repoId && (
                            <div>
                                <label>
                                    <span className="inline-block mb-[12px] text-[16px] text-[#333] font-semibold leading-[22px]">
                                        Branch
                                    </span>
                                    {type !== 'onboard-code' ? (
                                        <div className="flex items-center gap-[4px]">
                                            <InfoCircle
                                                width={16}
                                                height={16}
                                                color="#333"
                                            />
                                            <p className="text-[16px] text-[#333]">
                                                Blitzy will create a new branch
                                            </p>
                                        </div>
                                    ) : (
                                        <ComboSelect
                                            value={branchName}
                                            options={branchList}
                                            onChange={handleBranchChange}
                                            placeholder="Select branch"
                                            itemsLoading={branchListLoading}
                                            loadingMessage="Loading..."
                                            emptyMessage="No results found"
                                        />
                                    )}
                                </label>
                            </div>
                        )}
                    </div>
                </div>
                {orgName &&
                    repoId &&
                    (type !== 'onboard-code' || branchName) && (
                        <div className="flex justify-end items-center gap-[24px]">
                            <LoadingSubmitButton
                                loading={isSubmitting || isDocumentSubmitting}
                                loadingText={
                                    type === 'new-product'
                                        ? 'Build codebase'
                                        : type === 'refactor-code'
                                          ? 'Refactor codebase'
                                          : type === 'custom'
                                            ? 'Generate Code'
                                            : 'Continue'
                                }
                                onClick={handleContinue}
                                className="primary-button w-auto ">
                                <span className="flex justify-center items-center gap-[8px]">
                                    {type === 'new-product' ? (
                                        <CodeCurly />
                                    ) : type === 'refactor-code' ? (
                                        <Revision
                                            color="white"
                                            width={20}
                                            height={19.1}
                                        />
                                    ) : type === 'custom' ? (
                                        <Wrench
                                            color="white"
                                            width={20}
                                            height={19.1}
                                        />
                                    ) : null}
                                    {type === 'new-product'
                                        ? 'Build Codebase'
                                        : type === 'refactor-code'
                                          ? 'Refactor Codebase'
                                          : type === 'custom'
                                            ? 'Generate Code'
                                            : 'Continue'}
                                </span>
                            </LoadingSubmitButton>
                        </div>
                    )}
            </div>
            <UpgradeModal
                isOpen={showUpgradeModal}
                onClose={() => setShowUpgradeModal(false)}
            />
        </ProjectPanelContent>
    );
}
